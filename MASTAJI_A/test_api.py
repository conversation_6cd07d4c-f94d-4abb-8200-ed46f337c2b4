#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_api():
    """测试后端API"""
    base_url = "http://localhost:8080"
    
    # 创建session
    session = requests.Session()
    
    # 登录
    login_data = {
        'user_id': 'T001',
        'password': '123'
    }
    
    print("正在登录...")
    login_response = session.post(f"{base_url}/login", data=login_data)
    print(f"登录状态码: {login_response.status_code}")
    
    if login_response.status_code == 200:
        print("登录成功！")
        
        # 获取课程安排
        print("正在获取课程安排...")
        courses_response = session.get(f"{base_url}/teacher/get_course_schedules")
        print(f"课程获取状态码: {courses_response.status_code}")
        
        if courses_response.status_code == 200:
            data = courses_response.json()
            print(f"API响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('status') == 'success':
                all_courses = data.get('data', [])
                in_progress_courses = [course for course in all_courses if course.get('status') == 'in_progress']
                print(f"正在进行的课程数量: {len(in_progress_courses)}")
                for course in in_progress_courses:
                    print(f"课程: {course.get('course_name')} - {course.get('class_name')}")
            else:
                print(f"获取课程失败: {data.get('message')}")
        else:
            print(f"获取课程失败，状态码: {courses_response.status_code}")
            print(f"响应内容: {courses_response.text}")
    else:
        print(f"登录失败，状态码: {login_response.status_code}")
        print(f"响应内容: {login_response.text}")

if __name__ == '__main__':
    test_api()
